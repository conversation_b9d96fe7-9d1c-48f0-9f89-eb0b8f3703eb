import { useState, useEffect } from "react";
import { useLocation, useRoute } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { fetchAPI } from "@/lib/api";
import { formatDate } from "@/lib/utils";
import { Loader2, ChevronLeft , PlusCircle } from "lucide-react";

export default function JobCardFromQuotation() {
  const [, setLocation] = useLocation();
  const [match, params] = useRoute("job-card/initiate-from-quotation/:id");
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const quotationId = params?.id;
  const [quotation, setQuotation] = useState<any>(null);

  const [isLoading, setIsLoading] = useState(false);
  // Fetch quotation details from quotation id

  useEffect(() => {
    if (quotationId) {
      fetchQuotationDetails();
    }
  }, [quotationId]);

  const fetchQuotationDetails = async () => {
    console.log("Fetching quotation details for ID:", quotationId);
    try {
      const response = await fetchAPI(
        "GET",
        `/gate-pass/quotation/details/${quotationId}`
      );
      if (!response.ok) {
        throw new Error("Failed to fetch quotation");
      }
      const data = await response.json();
      setQuotation(data);
      return data;
    } catch (error) {
      console.error("Error fetching quotation:", error);
      toast({
        title: "Error",
        description: "Failed to fetch quotation. Please try again.",
        variant: "destructive",
      });
    }
  };

  const totalCost = () => {
    const serviceCost = quotation?.servicesUsed
      ? quotation?.servicesUsed.reduce(
          (total, service) => total + parseFloat(service.price || 0),
          0
        )
      : 0;
    const partsCost = quotation?.partsUsed
      ? quotation?.partsUsed.reduce(
          (total, part) => total + parseFloat(part.price || 0),
          0
        )
      : 0;
    return serviceCost + partsCost;
  };

  //   const { data: quotation, isLoading } = useQuery({
  //     queryKey: [`/api/quotations/${quotationId}`],
  //     queryFn: async () => {
  //       const response = await fetchAPI("GET", `/api/quotations/${quotationId}`);
  //       return response.json();
  //     },
  //     enabled: !!quotationId,
  //   });

  //   const handleCreateJobCard = () => {
  //     if (!quotation || !gatePass) return;

  //     const jobCardData = {
  //       gatePassId: quotation.gatePassId,
  //       vehicleId: gatePass.vehicleId,
  //       customerId: gatePass.customerId || null, // Assuming customer ID is in gate pass
  //       description: gatePass.jobDescription,
  //       status: "IN_PROGRESS",
  //       estimatedCompletionDate: gatePass.estimatedDeliveryDate,
  //       partsUsed: quotation.partsUsed,
  //       servicesUsed: quotation.servicesUsed,
  //       quotationId: quotation.id,
  //       notes: quotation.notes || "",
  //     };

  //     createJobCardMutation.mutate(jobCardData);
  //   };

  const createJobCard = async () => {
    if (!quotation) return;

    setIsLoading(true);

    try {
      const response = await fetchAPI(
        "POST",
        `/gate-pass/quotation/initiate/${quotationId}`
      );
      if (!response.ok) {
        throw new Error("Failed to create job card");
      }
      const data = await response.json();
      toast({
        title: "Job Card Created",
        description: "Job card has been created successfully from the quotation",
      });
      setLocation(`/job-card-details/${data.id}`);
    } catch (error) {
      console.error("Error creating job card:", error);
      toast({
        title: "Error",
        description: "Failed to create job card from quotation",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };    





  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
        <div className="flex justify-between items-center mb-6">
            <Button
              variant="outline"
              onClick={() => setLocation("/quotation")}
              className=" print-hidden"
            >
              <ChevronLeft className="mr-2 h-4 w-4" /> Back
            </Button>
      <Button
        onClick={() => createJobCard()}
      >
        <PlusCircle className=" h-5 w-5" />   Create Job Card
      </Button>
      </div>





      <div className="content">
        <div className="header">M R S AUTO MAINTENANCE L.L.C</div>
        <div className="sub-header">
          Tel: +971 55 994 1284, +971 55 994 1285 | Email: <EMAIL>
        </div>
        <h2
          style={{
            textAlign: "center",
            fontWeight: "bold",
            marginBottom: "30px",
          }}
        >
          JOB CARD
        </h2>
        <table className="details-table">
          <tbody>
            <tr>
              <td>
                <strong>JOBCARD ID:</strong> {quotation?.id || ""}
              </td>
              <td>
                <strong>Date:</strong> {new Date().toLocaleDateString()}
              </td>
              <td>
                <strong>Time:</strong>{" "}
                {new Date().toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                })}
              </td>
            </tr>
            <tr>
              <td>
                <strong>Driver Name:</strong>{" "}
                {quotation?.gatePass?.customerName || ""}
              </td>
              <td>
                <strong>Make & Model:</strong> {quotation?.vehicle?.make || ""}{" "}
                {quotation?.vehicle?.model_model || ""}
              </td>
              <td>
                <strong>Assigned to:</strong> {"Unassigned"}
              </td>
            </tr>
            <tr>
              <td>
                <strong>Chassis No:</strong>{" "}
                {quotation?.vehicle?.chassisNumber || ""}
              </td>
              <td>
                <strong>Engine No:</strong>{" "}
                {quotation?.vehicle?.engineNumber || ""}
              </td>
              <td>
                <strong>Reg No:</strong>{" "}
                {quotation?.vehicle?.vehicleRegistrationNumber || ""}
              </td>
            </tr>
            <tr>
              <td>
                <strong>Delivery Date:</strong>{" "}
                {quotation?.gatePass?.estimatedDeliveryDate
                  ? new Date(
                      quotation?.gatePass.estimatedDeliveryDate
                    ).toLocaleDateString()
                  : ""}
              </td>
              <td>
                <strong>Color:</strong> {quotation?.vehicle?.color || "Unknown"}
              </td>
              <td>
                <strong>ODO Meter:</strong> {quotation?.gatePass?.mileage || ""}
              </td>
            </tr>
            <tr>
              <td>
                <strong>Mobile No:</strong> {quotation?.gatePass?.phone || ""}
              </td>
              <td colSpan={2}>
                <strong>Veh. Reg. Card:</strong>{" "}
                {quotation?.gatePass?.vehicle?.registrationCard ? "Y" : "N"}
              </td>
            </tr>
          </tbody>
        </table>
        <br />
        <table className="billing-table">
          <thead>
            <tr>
              <th style={{ width: "5%" }}>S.N</th>
              <th style={{ width: "55%" }}>Description</th>
              <th style={{ width: "10%" }}>Qty</th>
              <th style={{ width: "10%" }}>Unit</th>
              <th style={{ width: "10%" }}>Price</th>
              <th style={{ width: "10%" }}>Amount (AED)</th>
            </tr>
          </thead>
          <tbody>
            {/* This would be populated with actual service/parts data */}
            {/* <tr> */}

            {quotation?.servicesUsed?.map((service: any, idx: number) => (
              <tr key={`service-${service.id}`}>
                <td>{idx + 1}</td>
                <td>{service.itemName || ""}</td>
                <td>1.00</td>
                <td>Service</td>
                <td>{parseFloat(service.price).toFixed(2)}</td>
                <td>{parseFloat(service.price).toFixed(2)}</td>
              </tr>
            ))}
            {quotation?.partsUsed?.map((part: any, idx: number) => (
              <tr key={`part-${part.id}`}>
                <td>{idx + 1}</td>
                <td>{part.itemName || ""}</td>
                <td>1.00</td>
                <td>Item</td>
                <td>{parseFloat(part.price).toFixed(2)}</td>
                <td>{parseFloat(part.price).toFixed(2)}</td>
              </tr>
            ))}
            {/* <td>&nbsp;</td><td></td><td></td><td></td><td></td><td></td></tr> */}

            <tr>
              <td>&nbsp;</td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td>&nbsp;</td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
            <tr>
              <td>&nbsp;</td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
          </tbody>
        </table>
        <br />
        <table className="summary-table">
          <tbody>
            <tr>
              <td className="label">Total</td>
              <td className="value">{totalCost().toFixed(2)}</td>
            </tr>
            <tr>
              <td className="label">Less : Excess</td>
              <td className="value">0.00</td>
            </tr>
            <tr>
              <td className="label">Less : Discount</td>
              <td className="value">0.00</td>
            </tr>
            <tr>
              <td className="label">Add : VAT @ 5.00%</td>
              <td className="value">{(totalCost() * 0.05).toFixed(2)}</td>
            </tr>
            <tr className="grand-total-row">
              <td className="label">Grand Total</td>
              <td className="value">{(totalCost() * 1.05).toFixed(2)}</td>
            </tr>
          </tbody>
        </table>
        <br />
        <strong>Dirhams Zero Only</strong>
        <div className="declaration">
          <strong>DECLARATION</strong>
          <br />
          I hereby authorize your garage to repair my vehicle.
          <br />
          <br />
          Name: ______________________ <br />
          Signature: __________________
        </div>
        <div className="signature">
          for <strong>M R S Auto Maintenance LLC</strong>
          <br />
          Authorised Signatory
        </div>
      </div>
    </div>
  );
}
